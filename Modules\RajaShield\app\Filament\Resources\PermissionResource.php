<?php

namespace Modules\RajaShield\Filament\Resources;

use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Columns\BadgeColumn;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Textarea;
use Filament\Tables\Actions\EditAction;
use Filament\Tables\Actions\DeleteAction;
use Filament\Tables\Actions\BulkActionGroup;
use Filament\Tables\Actions\DeleteBulkAction;
use Filament\Tables\Actions\Action;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Grouping\Group;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Route;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;
use Modules\RajaShield\Filament\Resources\PermissionResource\Pages;

class PermissionResource extends Resource
{
    protected static ?string $model = Permission::class;

    protected static ?string $navigationIcon = 'heroicon-o-key';
    protected static ?string $navigationGroup = 'Manajemen Akses';
    protected static ?string $navigationLabel = 'Permission';
    protected static ?string $modelLabel = 'Permission';
    protected static ?string $pluralModelLabel = 'Permissions';
    protected static ?int $navigationSort = 2;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Section::make('Informasi Permission')
                    ->description('Informasi dasar tentang permission')
                    ->schema([
                        TextInput::make('name')
                            ->label('Nama Permission')
                            ->required()
                            ->unique(ignoreRecord: true)
                            ->maxLength(255)
                            ->placeholder('Contoh: view_users, create_posts, delete_comments')
                            ->helperText('Gunakan format: action_resource (contoh: view_users, create_posts)')
                            ->live(onBlur: true)
                            ->afterStateUpdated(function ($state, callable $set) {
                                // Auto-generate guard_name berdasarkan nama
                                if ($state) {
                                    $set('guard_name', 'web');
                                }
                            }),

                        TextInput::make('guard_name')
                            ->label('Guard Name')
                            ->default('web')
                            ->required()
                            ->maxLength(255)
                            ->helperText('Guard yang digunakan untuk autentikasi'),

                        Textarea::make('description')
                            ->label('Deskripsi')
                            ->placeholder('Deskripsi singkat tentang permission ini')
                            ->maxLength(500)
                            ->rows(3),
                    ])
                    ->columns(2),

                Section::make('Informasi Tambahan')
                    ->description('Informasi tambahan untuk permission')
                    ->schema([
                        Select::make('category')
                            ->label('Kategori')
                            ->options(static::getPermissionCategories())
                            ->placeholder('Pilih kategori permission')
                            ->helperText('Kategori untuk mengelompokkan permissions'),

                        TextInput::make('route_name')
                            ->label('Route Name')
                            ->placeholder('Contoh: admin.users.index')
                            ->helperText('Nama route yang terkait dengan permission ini (opsional)'),
                    ])
                    ->columns(2)
                    ->collapsible(),
            ]);
    }

    protected static function getPermissionCategories(): array
    {
        // Ambil kategori dari permissions yang sudah ada
        $permissions = Permission::all();
        $categories = [];

        foreach ($permissions as $permission) {
            $parts = explode('_', $permission->name);
            if (count($parts) >= 2) {
                $category = $parts[1]; // Ambil bagian kedua sebagai kategori
                $categories[$category] = ucfirst($category);
            }
        }

        // Tambahkan kategori default
        $defaultCategories = [
            'users' => 'Users',
            'roles' => 'Roles',
            'permissions' => 'Permissions',
            'posts' => 'Posts',
            'pages' => 'Pages',
            'settings' => 'Settings',
            'dashboard' => 'Dashboard',
        ];

        return array_merge($defaultCategories, $categories);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('name')
                    ->label('Nama Permission')
                    ->searchable()
                    ->sortable()
                    ->weight('bold')
                    ->copyable(),

                TextColumn::make('guard_name')
                    ->label('Guard')
                    ->badge()
                    ->color('primary'),

                TextColumn::make('category')
                    ->label('Kategori')
                    ->getStateUsing(function ($record) {
                        $parts = explode('_', $record->name);
                        return count($parts) >= 2 ? ucfirst($parts[1]) : 'Other';
                    })
                    ->badge()
                    ->color('secondary'),

                TextColumn::make('action')
                    ->label('Aksi')
                    ->getStateUsing(function ($record) {
                        $parts = explode('_', $record->name);
                        return count($parts) >= 1 ? ucfirst($parts[0]) : 'Unknown';
                    })
                    ->badge()
                    ->color(fn ($state) => match($state) {
                        'View' => 'info',
                        'Create' => 'success',
                        'Update', 'Edit' => 'warning',
                        'Delete' => 'danger',
                        default => 'gray',
                    }),

                TextColumn::make('roles_count')
                    ->label('Digunakan di Role')
                    ->counts('roles')
                    ->badge()
                    ->color('success'),

                TextColumn::make('users_count')
                    ->label('Users Langsung')
                    ->counts('users')
                    ->badge()
                    ->color('info'),

                TextColumn::make('description')
                    ->label('Deskripsi')
                    ->limit(50)
                    ->tooltip(function (TextColumn $column): ?string {
                        $state = $column->getState();
                        return strlen($state) > 50 ? $state : null;
                    })
                    ->toggleable(isToggledHiddenByDefault: true),

                TextColumn::make('created_at')
                    ->label('Dibuat')
                    ->dateTime('d/m/Y H:i')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->groups([
                Group::make('category')
                    ->label('Kategori')
                    ->getDescriptionFromRecordUsing(function ($record) {
                        $parts = explode('_', $record->name);
                        return count($parts) >= 2 ? 'Permissions untuk ' . ucfirst($parts[1]) : 'Permissions lainnya';
                    }),
            ])
            ->filters([
                SelectFilter::make('guard_name')
                    ->label('Guard')
                    ->options([
                        'web' => 'Web',
                        'api' => 'API',
                    ]),

                SelectFilter::make('category')
                    ->label('Kategori')
                    ->options(static::getPermissionCategories())
                    ->query(function (Builder $query, array $data): Builder {
                        if (! $data['value']) {
                            return $query;
                        }
                        return $query->where('name', 'like', '%_' . $data['value'] . '%');
                    }),

                SelectFilter::make('roles')
                    ->label('Digunakan di Role')
                    ->relationship('roles', 'name')
                    ->multiple()
                    ->preload(),
            ])
            ->actions([
                EditAction::make()
                    ->label('Edit'),
                DeleteAction::make()
                    ->label('Hapus'),
            ])
            ->bulkActions([
                BulkActionGroup::make([
                    DeleteBulkAction::make()
                        ->label('Hapus Terpilih'),
                ]),
            ])
            ->headerActions([
                Action::make('generate_permissions')
                    ->label('Generate Permissions')
                    ->icon('heroicon-o-cog-6-tooth')
                    ->color('warning')
                    ->action(function () {
                        static::generatePermissionsFromRoutes();
                    })
                    ->requiresConfirmation()
                    ->modalHeading('Generate Permissions dari Routes')
                    ->modalDescription('Ini akan membuat permissions baru berdasarkan routes yang ada. Permissions yang sudah ada tidak akan diubah.')
                    ->modalSubmitActionLabel('Generate'),
            ])
            ->defaultSort('name', 'asc')
            ->defaultGroup('category');
    }

    protected static function generatePermissionsFromRoutes(): void
    {
        $routes = Route::getRoutes();
        $generatedCount = 0;

        foreach ($routes as $route) {
            $routeName = $route->getName();
            
            if (!$routeName || str_starts_with($routeName, 'filament.')) {
                continue;
            }

            // Generate permission name dari route name
            $permissionName = str_replace('.', '_', $routeName);
            
            // Cek apakah permission sudah ada
            if (!Permission::where('name', $permissionName)->exists()) {
                Permission::create([
                    'name' => $permissionName,
                    'guard_name' => 'web',
                    'description' => 'Auto-generated from route: ' . $routeName,
                ]);
                $generatedCount++;
            }
        }

        \Filament\Notifications\Notification::make()
            ->success()
            ->title('Permissions Generated')
            ->body("Berhasil generate {$generatedCount} permissions baru dari routes.")
            ->send();
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListPermissions::route('/'),
            'create' => Pages\CreatePermission::route('/create'),
            'edit' => Pages\EditPermission::route('/{record}/edit'),
        ];
    }

    public static function getNavigationBadge(): ?string
    {
        return number_format(static::getModel()::count());
    }

    public static function canViewAny(): bool
    {
        return Auth::check() && (
            Auth::user()->hasRole('super-admin') || 
            Auth::user()->can('view_permission')
        );
    }

    public static function canCreate(): bool
    {
        return Auth::check() && (
            Auth::user()->hasRole('super-admin') || 
            Auth::user()->can('create_permission')
        );
    }

    public static function canEdit($record): bool
    {
        return Auth::check() && (
            Auth::user()->hasRole('super-admin') || 
            Auth::user()->can('update_permission')
        );
    }

    public static function canDelete($record): bool
    {
        return Auth::check() && (
            Auth::user()->hasRole('super-admin') || 
            Auth::user()->can('delete_permission')
        );
    }
}
