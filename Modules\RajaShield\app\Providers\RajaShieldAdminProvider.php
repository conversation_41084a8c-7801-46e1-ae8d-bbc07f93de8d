<?php

namespace Modules\RajaShield\Providers;

use Filament\Facades\Filament;
use Illuminate\Support\ServiceProvider;
use Modules\RajaShield\Filament\Resources\RoleResource;
use Modules\RajaShield\Filament\Resources\PermissionResource;
use Modules\RajaShield\Filament\Pages\PermissionManager;

class RajaShieldAdminProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        // Register resources to admin panel
        $this->registerResourcesToAdminPanel();
    }

    /**
     * Register RajaShield resources to admin panel
     */
    protected function registerResourcesToAdminPanel(): void
    {
        try {
            // Try to get admin panel
            $adminPanel = Filament::getPanel('admin');

            if ($adminPanel) {
                // Register resources using discovery method
                $this->registerResourcesUsingDiscovery();
            }
        } catch (\Exception $e) {
            // Admin panel might not exist or not ready yet
            // Resources will be discovered automatically by Filament
            // if they are in the correct namespace and directory
        }
    }

    /**
     * Register resources using Filament's discovery mechanism
     */
    protected function registerResourcesUsingDiscovery(): void
    {
        // Resources will be auto-discovered by Filament if they follow the naming convention
        // and are in the correct directory structure. No manual registration needed.

        // The resources are already in the correct location:
        // - modules/RajaShield/app/Filament/Resources/RoleResource.php
        // - modules/RajaShield/app/Filament/Resources/PermissionResource.php
        // - modules/RajaShield/app/Filament/Pages/PermissionManager.php
    }
}
