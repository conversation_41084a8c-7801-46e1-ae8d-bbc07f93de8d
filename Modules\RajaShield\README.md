# Raja Shield

Modul manajemen role dan permission untuk <PERSON>vel dengan integrasi FilamentPHP, menggunakan Spatie <PERSON>-Permission. Modul ini menyediakan interface yang mirip dengan bezhansalleh/filament-shield untuk mengelola roles dan permissions.

## Fitur

- ✅ **Role Management**: Kelola roles dengan mudah melalui interface FilamentPHP
- ✅ **Permission Management**: Kelola permissions dengan grouping dan filtering
- ✅ **Auto-Discovery**: Generate permissions otomatis dari routes dan models
- ✅ **Super Admin**: Role super admin dengan akses penuh
- ✅ **Middleware**: Middleware untuk checking permissions berdasarkan route
- ✅ **Helper Classes**: Helper untuk manajemen permissions yang efisien
- ✅ **Caching**: Cache permissions untuk performa optimal
- ✅ **Admin Integration**: Terintegrasi dengan panel admin yang sudah ada

## Instalasi

1. **Pastikan Spatie Permission sudah terinstall**:
```bash
composer require spatie/laravel-permission
php artisan vendor:publish --provider="Spatie\Permission\PermissionServiceProvider"
php artisan migrate
```

2. **Setup Model User**:
Pastikan model User menggunakan trait HasRoles:
```php
use Spatie\Permission\Traits\HasRoles;

class User extends Authenticatable
{
    use HasRoles;
    // ...
}
```

3. **Setup RajaShield**:
```bash
php artisan rajashield:setup --routes --models --super-admin=<EMAIL>
```

## Penggunaan

### 1. Akses Resources di Panel Admin

Resources RajaShield akan muncul di panel admin yang sudah ada dalam navigation group "Manajemen Akses".

### 2. Integrasi dengan Panel Admin

Resources RajaShield akan otomatis terdaftar di panel admin yang sudah ada. Anda akan melihat:
- **Role** - Manajemen roles
- **Permission** - Manajemen permissions
- **Permission Manager** - Dashboard manajemen permissions

> **Catatan**: Untuk konfigurasi integrasi yang lebih detail, lihat [INTEGRATION.md](INTEGRATION.md)

### 3. Menggunakan Middleware

Daftarkan middleware di `app/Http/Kernel.php`:
```php
protected $middlewareAliases = [
    'raja.shield' => \Modules\RajaShield\Http\Middleware\RajaShieldMiddleware::class,
];
```

Atau tambahkan ke panel admin di `AdminPanelProvider.php`:
```php
->authMiddleware([
    Authenticate::class,
    \Modules\RajaShield\Http\Middleware\RajaShieldMiddleware::class,
])
```

### 4. Menggunakan Helper Classes

**SuperAdminHelper**:
```php
use Modules\RajaShield\Helpers\SuperAdminHelper;

// Cek apakah user adalah super admin
if (SuperAdminHelper::isSuperAdmin()) {
    // User adalah super admin
}

// Assign super admin role
SuperAdminHelper::assignSuperAdminRole($userId);
```

**ModelPermissionHelper**:
```php
use Modules\RajaShield\Helpers\ModelPermissionHelper;

// Generate permissions untuk model
ModelPermissionHelper::generateModelPermissions('Post');

// Generate permissions untuk semua models
ModelPermissionHelper::generateAllModelPermissions();
```

### 5. Checking Permissions di FilamentPHP Resources

```php
class PostResource extends Resource
{
    public static function canViewAny(): bool
    {
        return Auth::check() && (
            Auth::user()->hasRole('super-admin') ||
            Auth::user()->can('view_posts')
        );
    }

    public static function canCreate(): bool
    {
        return Auth::check() && (
            Auth::user()->hasRole('super-admin') ||
            Auth::user()->can('create_posts')
        );
    }
}
```

## Konfigurasi

File konfigurasi tersedia di `modules/RajaShield/config/config.php`:

```php
return [
    'super_admin_role' => 'super-admin',
    'default_guard' => 'web',
    'cache' => [
        'enabled' => true,
        'duration' => 300,
    ],
    'middleware' => [
        'enabled' => true,
        'excluded_routes' => [
            // Routes yang dikecualikan dari permission checking
        ],
    ],
    // ... konfigurasi lainnya
];
```

## Commands

### Setup Command
```bash
# Setup lengkap dengan generate permissions dan super admin
php artisan rajashield:setup --routes --models --super-admin=<EMAIL>

# Setup ulang (force)
php artisan rajashield:setup --force

# Hanya generate dari routes
php artisan rajashield:setup --routes

# Hanya generate dari models
php artisan rajashield:setup --models
```

## Struktur Modul

```
modules/RajaShield/
├── app/
│   ├── Console/Commands/
│   │   └── RajaShieldSetupCommand.php
│   ├── Filament/
│   │   ├── Pages/
│   │   │   └── PermissionManager.php
│   │   └── Resources/
│   │       ├── RoleResource.php
│   │       └── PermissionResource.php
│   ├── Helpers/
│   │   ├── SuperAdminHelper.php
│   │   └── ModelPermissionHelper.php
│   ├── Http/Middleware/
│   │   └── RajaShieldMiddleware.php
│   └── Providers/
│       ├── RajaShieldServiceProvider.php
│       └── RajaShieldAdminProvider.php
├── config/
│   └── config.php
└── resources/
    └── views/
        └── pages/
            └── permission-manager.blade.php
```

## Permissions Standar

Untuk setiap model, permissions berikut akan dibuat:
- `view_{model}` - Melihat record individual
- `view_any_{model}` - Melihat daftar records
- `create_{model}` - Membuat record baru
- `update_{model}` - Mengupdate record
- `delete_{model}` - Menghapus record
- `delete_any_{model}` - Bulk delete
- `force_delete_{model}` - Force delete (soft delete)
- `restore_{model}` - Restore soft deleted record

## Security

- Role `super-admin` tidak dapat dihapus
- Super admin memiliki akses ke semua permissions
- Middleware mengecek permissions berdasarkan route name
- Cache permissions untuk performa optimal

## Changelog

Please see [CHANGELOG](CHANGELOG.md) for more information on what has changed recently.

## License

The MIT License (MIT). Please see [License File](LICENSE.md) for more information.
