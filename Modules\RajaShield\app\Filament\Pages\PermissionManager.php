<?php

namespace Modules\RajaShield\Filament\Pages;

use Filament\Pages\Page;
use Filament\Actions\Action;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\Section;
use Filament\Forms\Form;
use Filament\Notifications\Notification;
use Filament\Tables\Concerns\InteractsWithTable;
use Filament\Tables\Contracts\HasTable;
use Filament\Tables\Table;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Actions\Action as TableAction;
use Illuminate\Support\Facades\Auth;
use Modules\RajaShield\Helpers\SuperAdminHelper;
use Modules\RajaShield\Helpers\ModelPermissionHelper;
use Modules\RajaShield\Http\Middleware\RajaShieldMiddleware;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;

class PermissionManager extends Page implements HasTable
{
    use InteractsWithTable;

    protected static ?string $navigationIcon = 'heroicon-o-cog-6-tooth';
    protected static ?string $navigationGroup = 'Manajemen Akses';
    protected static ?string $navigationLabel = 'Permission Manager';
    protected static ?string $title = 'Permission Manager';
    protected static ?int $navigationSort = 3;

    protected static string $view = 'rajashield::pages.permission-manager';

    public function mount(): void
    {
        // Cek akses
        if (!SuperAdminHelper::isSuperAdmin()) {
            abort(403, 'Hanya super admin yang dapat mengakses halaman ini.');
        }
    }

    protected function getHeaderActions(): array
    {
        return [
            Action::make('generate_from_routes')
                ->label('Generate dari Routes')
                ->icon('heroicon-o-arrow-path')
                ->color('warning')
                ->action(function () {
                    $count = RajaShieldMiddleware::generatePermissionsFromRoutes();
                    
                    Notification::make()
                        ->success()
                        ->title('Permissions Generated')
                        ->body("Berhasil generate {$count} permissions baru dari routes.")
                        ->send();
                })
                ->requiresConfirmation()
                ->modalHeading('Generate Permissions dari Routes')
                ->modalDescription('Ini akan membuat permissions baru berdasarkan routes yang ada. Permissions yang sudah ada tidak akan diubah.'),

            Action::make('generate_from_models')
                ->label('Generate dari Models')
                ->icon('heroicon-o-cube')
                ->color('success')
                ->action(function () {
                    $permissions = ModelPermissionHelper::generateAllModelPermissions();
                    $count = count($permissions);
                    
                    Notification::make()
                        ->success()
                        ->title('Model Permissions Generated')
                        ->body("Berhasil generate {$count} permissions untuk models.")
                        ->send();
                })
                ->requiresConfirmation()
                ->modalHeading('Generate Permissions dari Models')
                ->modalDescription('Ini akan membuat permissions standar untuk semua models yang ditemukan.'),

            Action::make('sync_super_admin')
                ->label('Sync Super Admin')
                ->icon('heroicon-o-shield-check')
                ->color('danger')
                ->action(function () {
                    SuperAdminHelper::syncAllPermissionsToSuperAdmin();
                    
                    Notification::make()
                        ->success()
                        ->title('Super Admin Synced')
                        ->body('Semua permissions telah di-sync ke role super-admin.')
                        ->send();
                })
                ->requiresConfirmation()
                ->modalHeading('Sync Permissions ke Super Admin')
                ->modalDescription('Ini akan memberikan semua permissions yang ada ke role super-admin.'),
        ];
    }

    public function table(Table $table): Table
    {
        return $table
            ->query(Permission::query())
            ->columns([
                TextColumn::make('name')
                    ->label('Permission Name')
                    ->searchable()
                    ->sortable(),

                TextColumn::make('guard_name')
                    ->label('Guard')
                    ->badge(),

                TextColumn::make('roles_count')
                    ->label('Used in Roles')
                    ->counts('roles')
                    ->badge()
                    ->color('success'),

                TextColumn::make('users_count')
                    ->label('Direct Users')
                    ->counts('users')
                    ->badge()
                    ->color('info'),

                TextColumn::make('created_at')
                    ->label('Created')
                    ->dateTime()
                    ->sortable(),
            ])
            ->actions([
                TableAction::make('delete')
                    ->label('Delete')
                    ->icon('heroicon-o-trash')
                    ->color('danger')
                    ->action(function (Permission $record) {
                        $record->delete();
                        
                        Notification::make()
                            ->success()
                            ->title('Permission Deleted')
                            ->body("Permission '{$record->name}' berhasil dihapus.")
                            ->send();
                    })
                    ->requiresConfirmation(),
            ])
            ->bulkActions([
                \Filament\Tables\Actions\DeleteBulkAction::make()
                    ->label('Delete Selected'),
            ]);
    }

    public function getStats(): array
    {
        return ModelPermissionHelper::getPermissionStats();
    }

    public static function canAccess(): bool
    {
        return Auth::check() && SuperAdminHelper::isSuperAdmin();
    }
}
