<?php

namespace Modules\RajaShield\Providers;

use Filament\Facades\Filament;
use Illuminate\Support\ServiceProvider;
use Modules\RajaShield\Filament\Resources\RoleResource;
use Modules\RajaShield\Filament\Resources\PermissionResource;
use Modules\RajaShield\Filament\Pages\PermissionManager;

class RajaShieldAdminProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        // Register resources to admin panel
        $this->registerResourcesToAdminPanel();
    }

    /**
     * Register RajaShield resources to admin panel
     */
    protected function registerResourcesToAdminPanel(): void
    {
        // Check if admin panel exists
        if (Filament::hasPanel('admin')) {
            $adminPanel = Filament::getPanel('admin');
            
            // Register resources
            $adminPanel->resources([
                RoleResource::class,
                PermissionResource::class,
            ]);

            // Register pages
            $adminPanel->pages([
                PermissionManager::class,
            ]);
        }
    }
}
