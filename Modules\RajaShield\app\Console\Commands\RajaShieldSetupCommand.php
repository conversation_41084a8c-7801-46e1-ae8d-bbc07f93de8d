<?php

namespace Modules\RajaShield\Console\Commands;

use Illuminate\Console\Command;
use Mo<PERSON>les\RajaShield\Helpers\SuperAdminHelper;
use Mo<PERSON>les\RajaShield\Helpers\ModelPermissionHelper;
use Modules\RajaShield\Http\Middleware\RajaShieldMiddleware;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;

class RajaShieldSetupCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'rajashield:setup 
                            {--force : Force setup even if already configured}
                            {--routes : Generate permissions from routes}
                            {--models : Generate permissions from models}
                            {--super-admin= : Create super admin user with email}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Setup RajaShield module with initial roles and permissions';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🛡️  Setting up RajaShield...');

        // Check if already setup
        if (!$this->option('force') && $this->isAlreadySetup()) {
            $this->warn('RajaShield sudah di-setup sebelumnya. Gunakan --force untuk setup ulang.');
            return;
        }

        // Create basic roles
        $this->createBasicRoles();

        // Generate permissions from routes if requested
        if ($this->option('routes')) {
            $this->generatePermissionsFromRoutes();
        }

        // Generate permissions from models if requested
        if ($this->option('models')) {
            $this->generatePermissionsFromModels();
        }

        // Create super admin user if email provided
        if ($this->option('super-admin')) {
            $this->createSuperAdminUser($this->option('super-admin'));
        }

        // Sync super admin permissions
        $this->syncSuperAdminPermissions();

        $this->info('✅ RajaShield setup completed successfully!');
        $this->displaySummary();
    }

    /**
     * Check if RajaShield is already setup
     */
    protected function isAlreadySetup(): bool
    {
        return Role::where('name', 'super-admin')->exists();
    }

    /**
     * Create basic roles
     */
    protected function createBasicRoles(): void
    {
        $this->info('📝 Creating basic roles...');

        $roles = [
            'super-admin' => 'Super Administrator - Full access to everything',
            'admin' => 'Administrator - Manage most resources',
            'manager' => 'Manager - Limited administrative access',
            'user' => 'Regular User - Basic access',
        ];

        foreach ($roles as $name => $description) {
            $role = Role::firstOrCreate([
                'name' => $name,
                'guard_name' => 'web',
            ]);

            $this->line("  ✓ Role '{$name}' created/updated");
        }
    }

    /**
     * Generate permissions from routes
     */
    protected function generatePermissionsFromRoutes(): void
    {
        $this->info('🛣️  Generating permissions from routes...');

        $count = RajaShieldMiddleware::generatePermissionsFromRoutes();
        $this->line("  ✓ Generated {$count} permissions from routes");
    }

    /**
     * Generate permissions from models
     */
    protected function generatePermissionsFromModels(): void
    {
        $this->info('📦 Generating permissions from models...');

        $permissions = ModelPermissionHelper::generateAllModelPermissions();
        $count = count($permissions);
        $this->line("  ✓ Generated {$count} permissions from models");
    }

    /**
     * Create super admin user
     */
    protected function createSuperAdminUser(string $email): void
    {
        $this->info("👤 Creating super admin user with email: {$email}");

        $user = \App\Models\User::firstOrCreate([
            'email' => $email,
        ], [
            'name' => 'Super Admin',
            'password' => bcrypt('password'),
            'email_verified_at' => now(),
        ]);

        SuperAdminHelper::assignSuperAdminRole($user->id);
        $this->line("  ✓ Super admin user created/updated");
        $this->warn("  ⚠️  Default password is 'password' - please change it!");
    }

    /**
     * Sync super admin permissions
     */
    protected function syncSuperAdminPermissions(): void
    {
        $this->info('🔄 Syncing super admin permissions...');

        SuperAdminHelper::syncAllPermissionsToSuperAdmin();
        $this->line("  ✓ Super admin permissions synced");
    }

    /**
     * Display setup summary
     */
    protected function displaySummary(): void
    {
        $this->newLine();
        $this->info('📊 Setup Summary:');
        $this->table(
            ['Item', 'Count'],
            [
                ['Total Roles', Role::count()],
                ['Total Permissions', Permission::count()],
                ['Super Admins', \App\Models\User::role('super-admin')->count()],
            ]
        );

        $this->newLine();
        $this->info('🚀 Next Steps:');
        $this->line('1. Visit your admin panel to access RajaShield resources');
        $this->line('2. Configure roles and permissions as needed');
        $this->line('3. Assign roles to users');
        $this->line('4. Test permission checking in your application');

        if ($this->option('super-admin')) {
            $this->newLine();
            $this->warn('🔐 Security Note:');
            $this->line('Please change the default password for the super admin user!');
        }
    }
}
