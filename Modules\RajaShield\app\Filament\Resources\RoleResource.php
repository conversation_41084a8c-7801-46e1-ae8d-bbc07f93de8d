<?php

namespace Modules\RajaShield\Filament\Resources;

use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Columns\BadgeColumn;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\CheckboxList;
use Filament\Forms\Components\Tabs;
use Filament\Forms\Components\Tabs\Tab;
use Filament\Tables\Actions\EditAction;
use Filament\Tables\Actions\DeleteAction;
use Filament\Tables\Actions\BulkActionGroup;
use Filament\Tables\Actions\DeleteBulkAction;
use Filament\Tables\Filters\SelectFilter;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Auth;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;
use Modules\RajaShield\Filament\Resources\RoleResource\Pages;

class RoleResource extends Resource
{
    protected static ?string $model = Role::class;

    protected static ?string $navigationIcon = 'heroicon-o-shield-check';
    protected static ?string $navigationGroup = 'Manajemen Akses';
    protected static ?string $navigationLabel = 'Role';
    protected static ?string $modelLabel = 'Role';
    protected static ?string $pluralModelLabel = 'Roles';
    protected static ?int $navigationSort = 1;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Section::make('Informasi Role')
                    ->description('Informasi dasar tentang role')
                    ->schema([
                        TextInput::make('name')
                            ->label('Nama Role')
                            ->required()
                            ->unique(ignoreRecord: true)
                            ->maxLength(255)
                            ->placeholder('Contoh: admin, manager, user')
                            ->helperText('Nama role harus unik dan tidak boleh sama dengan role lain'),

                        TextInput::make('guard_name')
                            ->label('Guard Name')
                            ->default('web')
                            ->required()
                            ->maxLength(255)
                            ->disabled()
                            ->helperText('Guard yang digunakan untuk autentikasi'),
                    ])
                    ->columns(2),

                Section::make('Permissions')
                    ->description('Pilih permissions yang akan diberikan ke role ini')
                    ->schema([
                        static::getPermissionTabs(),
                    ])
                    ->collapsible()
                    ->collapsed(false),
            ]);
    }

    protected static function getPermissionTabs(): Tabs
    {
        $permissions = Permission::all()->groupBy(function ($permission) {
            // Group berdasarkan prefix nama permission (sebelum titik pertama)
            $parts = explode('.', $permission->name);
            return $parts[0] ?? 'other';
        });

        $tabs = [];

        foreach ($permissions as $group => $groupPermissions) {
            $tabs[] = Tab::make(ucfirst($group))
                ->schema([
                    CheckboxList::make('permissions')
                        ->label('Permissions untuk ' . ucfirst($group))
                        ->relationship('permissions')
                        ->options($groupPermissions->pluck('name', 'id'))
                        ->descriptions($groupPermissions->mapWithKeys(function ($permission) {
                            return [$permission->id => $permission->name];
                        }))
                        ->columns(2)
                        ->gridDirection('row')
                        ->bulkToggleable(),
                ]);
        }

        // Tab untuk semua permissions
        $tabs[] = Tab::make('Semua')
            ->schema([
                CheckboxList::make('permissions')
                    ->label('Semua Permissions')
                    ->relationship('permissions')
                    ->options(Permission::all()->pluck('name', 'id'))
                    ->descriptions(Permission::all()->mapWithKeys(function ($permission) {
                        return [$permission->id => $permission->name];
                    }))
                    ->columns(3)
                    ->gridDirection('row')
                    ->bulkToggleable()
                    ->searchable(),
            ]);

        return Tabs::make('Permission Tabs')
            ->tabs($tabs);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('name')
                    ->label('Nama Role')
                    ->searchable()
                    ->sortable()
                    ->weight('bold'),

                TextColumn::make('guard_name')
                    ->label('Guard')
                    ->badge()
                    ->color('primary'),

                TextColumn::make('permissions_count')
                    ->label('Jumlah Permissions')
                    ->counts('permissions')
                    ->badge()
                    ->color('success'),

                TextColumn::make('users_count')
                    ->label('Jumlah Users')
                    ->counts('users')
                    ->badge()
                    ->color('info'),

                TextColumn::make('permissions.name')
                    ->label('Permissions')
                    ->listWithLineBreaks()
                    ->limitList(3)
                    ->expandableLimitedList()
                    ->wrap(),

                TextColumn::make('created_at')
                    ->label('Dibuat')
                    ->dateTime('d/m/Y H:i')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),

                TextColumn::make('updated_at')
                    ->label('Diperbarui')
                    ->dateTime('d/m/Y H:i')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                SelectFilter::make('guard_name')
                    ->label('Guard')
                    ->options([
                        'web' => 'Web',
                        'api' => 'API',
                    ]),

                SelectFilter::make('permissions')
                    ->label('Memiliki Permission')
                    ->relationship('permissions', 'name')
                    ->multiple()
                    ->preload(),
            ])
            ->actions([
                EditAction::make()
                    ->label('Edit'),
                DeleteAction::make()
                    ->label('Hapus')
                    ->before(function (Role $record) {
                        // Cegah penghapusan role super-admin
                        if ($record->name === 'super-admin') {
                            throw new \Exception('Role super-admin tidak dapat dihapus!');
                        }
                    }),
            ])
            ->bulkActions([
                BulkActionGroup::make([
                    DeleteBulkAction::make()
                        ->label('Hapus Terpilih')
                        ->before(function ($records) {
                            // Cegah penghapusan role super-admin
                            foreach ($records as $record) {
                                if ($record->name === 'super-admin') {
                                    throw new \Exception('Role super-admin tidak dapat dihapus!');
                                }
                            }
                        }),
                ]),
            ])
            ->defaultSort('name', 'asc');
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListRoles::route('/'),
            'create' => Pages\CreateRole::route('/create'),
            'edit' => Pages\EditRole::route('/{record}/edit'),
        ];
    }

    public static function getNavigationBadge(): ?string
    {
        return number_format(static::getModel()::count());
    }

    public static function canViewAny(): bool
    {
        return Auth::check() && (
            Auth::user()->hasRole('super-admin') || 
            Auth::user()->can('view_role')
        );
    }

    public static function canCreate(): bool
    {
        return Auth::check() && (
            Auth::user()->hasRole('super-admin') || 
            Auth::user()->can('create_role')
        );
    }

    public static function canEdit($record): bool
    {
        return Auth::check() && (
            Auth::user()->hasRole('super-admin') || 
            Auth::user()->can('update_role')
        );
    }

    public static function canDelete($record): bool
    {
        // Super admin tidak bisa dihapus
        if ($record->name === 'super-admin') {
            return false;
        }

        return Auth::check() && (
            Auth::user()->hasRole('super-admin') || 
            Auth::user()->can('delete_role')
        );
    }
}
