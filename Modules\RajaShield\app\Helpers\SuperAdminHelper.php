<?php

namespace Modules\RajaShield\Helpers;

use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;

class SuperAdminHelper
{
    /**
     * Cache key untuk super admin check
     */
    protected static string $cacheKey = 'raja_shield_super_admin_';

    /**
     * Cache duration dalam detik (5 menit)
     */
    protected static int $cacheDuration = 300;

    /**
     * Nama role super admin
     */
    protected static string $superAdminRole = 'super-admin';

    /**
     * Cek apakah user saat ini adalah super admin
     */
    public static function isSuperAdmin(?int $userId = null): bool
    {
        $userId = $userId ?? Auth::id();
        
        if (!$userId) {
            return false;
        }

        $cacheKey = static::$cacheKey . $userId;

        return Cache::remember($cacheKey, static::$cacheDuration, function () use ($userId) {
            $user = \App\Models\User::find($userId);
            return $user ? $user->hasRole(static::$superAdminRole) : false;
        });
    }

    /**
     * Cek apakah user memiliki akses ke semua permissions
     */
    public static function hasAllPermissions(?int $userId = null): bool
    {
        return static::isSuperAdmin($userId);
    }

    /**
     * Buat role super admin jika belum ada
     */
    public static function createSuperAdminRole(): Role
    {
        $role = Role::firstOrCreate([
            'name' => static::$superAdminRole,
            'guard_name' => 'web',
        ]);

        // Berikan semua permissions ke super admin
        $permissions = Permission::where('guard_name', 'web')->get();
        $role->syncPermissions($permissions);

        return $role;
    }

    /**
     * Assign super admin role ke user
     */
    public static function assignSuperAdminRole(int $userId): bool
    {
        $user = \App\Models\User::find($userId);
        
        if (!$user) {
            return false;
        }

        // Pastikan role super admin ada
        static::createSuperAdminRole();

        // Assign role
        $user->assignRole(static::$superAdminRole);

        // Clear cache
        static::clearCache($userId);

        return true;
    }

    /**
     * Remove super admin role dari user
     */
    public static function removeSuperAdminRole(int $userId): bool
    {
        $user = \App\Models\User::find($userId);
        
        if (!$user) {
            return false;
        }

        $user->removeRole(static::$superAdminRole);

        // Clear cache
        static::clearCache($userId);

        return true;
    }

    /**
     * Sync semua permissions ke super admin role
     */
    public static function syncAllPermissionsToSuperAdmin(): void
    {
        $role = Role::where('name', static::$superAdminRole)->first();
        
        if (!$role) {
            $role = static::createSuperAdminRole();
        }

        $permissions = Permission::where('guard_name', 'web')->get();
        $role->syncPermissions($permissions);

        // Clear cache untuk semua super admin
        static::clearAllSuperAdminCache();
    }

    /**
     * Clear cache untuk user tertentu
     */
    public static function clearCache(int $userId): void
    {
        $cacheKey = static::$cacheKey . $userId;
        Cache::forget($cacheKey);
    }

    /**
     * Clear cache untuk semua super admin
     */
    public static function clearAllSuperAdminCache(): void
    {
        $superAdmins = \App\Models\User::role(static::$superAdminRole)->get();
        
        foreach ($superAdmins as $admin) {
            static::clearCache($admin->id);
        }
    }

    /**
     * Get semua super admin users
     */
    public static function getAllSuperAdmins(): \Illuminate\Database\Eloquent\Collection
    {
        return \App\Models\User::role(static::$superAdminRole)->get();
    }

    /**
     * Cek apakah ada minimal satu super admin
     */
    public static function hasSuperAdmin(): bool
    {
        return \App\Models\User::role(static::$superAdminRole)->exists();
    }

    /**
     * Set nama role super admin (untuk kustomisasi)
     */
    public static function setSuperAdminRoleName(string $roleName): void
    {
        static::$superAdminRole = $roleName;
    }

    /**
     * Get nama role super admin
     */
    public static function getSuperAdminRoleName(): string
    {
        return static::$superAdminRole;
    }

    /**
     * Cek apakah user dapat mengakses resource tertentu
     */
    public static function canAccessResource(string $resource, ?int $userId = null): bool
    {
        if (static::isSuperAdmin($userId)) {
            return true;
        }

        $userId = $userId ?? Auth::id();
        $user = \App\Models\User::find($userId);

        if (!$user) {
            return false;
        }

        // Cek permission untuk resource
        $permissions = [
            "view_{$resource}",
            "view_any_{$resource}",
            "create_{$resource}",
            "update_{$resource}",
            "delete_{$resource}",
        ];

        foreach ($permissions as $permission) {
            if ($user->can($permission)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Generate permissions untuk resource tertentu
     */
    public static function generateResourcePermissions(string $resource): array
    {
        $permissions = [];
        $actions = ['view', 'view_any', 'create', 'update', 'delete', 'delete_any'];

        foreach ($actions as $action) {
            $permissionName = "{$action}_{$resource}";
            
            $permission = Permission::firstOrCreate([
                'name' => $permissionName,
                'guard_name' => 'web',
            ]);

            $permissions[] = $permission;
        }

        // Sync ke super admin role
        static::syncAllPermissionsToSuperAdmin();

        return $permissions;
    }
}
