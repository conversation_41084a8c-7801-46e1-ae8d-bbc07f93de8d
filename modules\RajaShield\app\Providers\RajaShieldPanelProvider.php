<?php

namespace Modules\RajaShield\Providers;

use Filament\Http\Middleware\Authenticate;
use Filament\Http\Middleware\DisableBladeIconComponents;
use Filament\Http\Middleware\DispatchServingFilamentEvent;
use Filament\Panel;
use Filament\PanelProvider;
use Filament\Support\Colors\Color;
use Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse;
use Illuminate\Cookie\Middleware\EncryptCookies;
use Illuminate\Foundation\Http\Middleware\VerifyCsrfToken;
use Illuminate\Routing\Middleware\SubstituteBindings;
use Illuminate\Session\Middleware\AuthenticateSession;
use Illuminate\Session\Middleware\StartSession;
use Illuminate\View\Middleware\ShareErrorsFromSession;
use Jeffgreco13\FilamentBreezy\BreezyCore;
use Modules\RajaShield\Filament\Pages\PermissionManager;
use Modules\RajaShield\Filament\Resources\PermissionResource;
use Modules\RajaShield\Filament\Resources\RoleResource;

class RajaShieldPanelProvider extends PanelProvider
{
    public function panel(Panel $panel): Panel
    {
        return $panel
            ->default()
            ->id('rajashield')
            ->path('/rajashield')
            ->login()
            ->colors([
                'primary' => Color::Amber,
            ])
            ->discoverResources(in: module_path('RajaShield', 'app/Filament/Resources'), for: 'Modules\\RajaShield\\Filament\\Resources')
            ->discoverPages(in: module_path('RajaShield', 'app/Filament/Pages'), for: 'Modules\\RajaShield\\Filament\\Pages')
            ->pages([
                PermissionManager::class,
            ])
            ->discoverWidgets(in: module_path('RajaShield', 'app/Filament/Widgets'), for: 'Modules\\RajaShield\\Filament\\Widgets')
            ->widgets([
                // Widgets akan ditambahkan di sini jika diperlukan
            ])
            ->middleware([
                EncryptCookies::class,
                AddQueuedCookiesToResponse::class,
                StartSession::class,
                AuthenticateSession::class,
                ShareErrorsFromSession::class,
                VerifyCsrfToken::class,
                SubstituteBindings::class,
                DisableBladeIconComponents::class,
                DispatchServingFilamentEvent::class,
            ])
            ->authMiddleware([
                Authenticate::class,
                \Modules\RajaShield\Http\Middleware\RajaShieldMiddleware::class,
            ])
            ->resources([
                RoleResource::class,
                PermissionResource::class,
            ])
            ->navigationGroups([
                'Manajemen Akses' => [
                    'label' => 'Manajemen Akses',
                    'icon' => 'heroicon-o-shield-check',
                    'sort' => 1,
                ],
            ])
            ->brandName('Raja Shield')
            ->brandLogo(asset('images/logo.png'))
            ->favicon(asset('images/favicon.ico'))
            ->darkMode(false)
            ->sidebarCollapsibleOnDesktop()
            ->navigationItems([
                // Navigation items tambahan jika diperlukan
            ])
            ->plugins([
                  \Solutionforest\FilamentScaffold\FilamentScaffoldPlugin::make(),

            
                \DutchCodingCompany\FilamentDeveloperLogins\FilamentDeveloperLoginsPlugin::make()
                    ->enabled()
                    ->users(fn() => \App\Models\User::pluck('email', 'name')->toArray()),
                \Datlechin\FilamentMenuBuilder\FilamentMenuBuilderPlugin::make()

                    ->addLocation('website_header', 'website_header')

                    ->showCustomTextPanel()

                    ->addMenuPanels([
                        \Datlechin\FilamentMenuBuilder\MenuPanel\StaticMenuPanel::make('Default')
                            ->add('Home', url('/'))
                            ->add('Artikel', url('/artikel')),
                    ])
                    ->addMenuPanels([
                        \Datlechin\FilamentMenuBuilder\MenuPanel\StaticMenuPanel::make('halaman')
                            ->addMany(
                                \App\Models\Cms::where('jenis', 'halaman')
                                    ->pluck('slug', 'judul')
                                    ->map(fn($slug) => "/halaman/{$slug}")
                                    ->toArray()
                            )
                            ->description('Lorem ipsum...')
                            ->icon('heroicon-m-link')
                            ->collapsed(true)
                            ->collapsible(true)
                            ->paginate(perPage: 5, condition: true)
                    ])->usingResource(\App\Filament\Resources\MenuWebsiteResource::class),
                BreezyCore::make()
                    ->myProfile(
                        shouldRegisterUserMenu: false, // Sets the 'account' link in the panel User Menu (default = true)
                        userMenuLabel: 'Profileku', // Customizes the 'account' link label in the panel User Menu (default = null)
                        shouldRegisterNavigation: false, // Adds a main navigation item for the My Profile page (default = false)
                        navigationGroup: 'Pengaturan', // Sets the navigation group for the My Profile page (default = null)
                        hasAvatars: false, // Enables the avatar upload form component (default = false)
                        slug: 'my-profile' // Sets the slug for the profile page (default = 'my-profile')
                    ),

                FilamentSpatieLaravelBackupPlugin::make()->usingPage(\App\Filament\Pages\Backup::class)->usingQueue('backupanku')->noTimeout(),
                \Croustibat\FilamentJobsMonitor\FilamentJobsMonitorPlugin::make(),

               

                \TomatoPHP\FilamentPlugins\FilamentPluginsPlugin::make()
                ->discoverCurrentPanelOnly() 
                , 

                \Visualbuilder\EmailTemplates\EmailTemplatesPlugin::make(),
            ])
            
            ;
    }
}
