<?php

namespace Modules\RajaShield\Filament\Resources\RoleResource\Pages;

use Filament\Actions;
use Filament\Resources\Pages\ListRecords;
use Filament\Resources\Components\Tab;
use Illuminate\Database\Eloquent\Builder;
use Modules\RajaShield\Filament\Resources\RoleResource;
use Spatie\Permission\Models\Role;

class ListRoles extends ListRecords
{
    protected static string $resource = RoleResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make()
                ->label('Tambah Role')
                ->icon('heroicon-o-plus'),
        ];
    }

    public function getTabs(): array
    {
        return [
            'semua' => Tab::make('Semua Role')
                ->badge(fn () => Role::count()),

            'web' => Tab::make('Web Guard')
                ->modifyQueryUsing(fn (Builder $query) => $query->where('guard_name', 'web'))
                ->badge(fn () => Role::where('guard_name', 'web')->count()),

            'api' => Tab::make('API Guard')
                ->modifyQueryUsing(fn (Builder $query) => $query->where('guard_name', 'api'))
                ->badge(fn () => Role::where('guard_name', 'api')->count()),
        ];
    }
}
