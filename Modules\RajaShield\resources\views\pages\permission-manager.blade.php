<x-filament-panels::page>
    <div class="space-y-6">
        <!-- Stats Cards -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
            @php
                $stats = $this->getStats();
            @endphp
            
            <div class="bg-white rounded-lg shadow p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <x-heroicon-o-key class="h-8 w-8 text-blue-500" />
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-500">Total Permissions</p>
                        <p class="text-2xl font-semibold text-gray-900">{{ $stats['total_permissions'] }}</p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <x-heroicon-o-shield-check class="h-8 w-8 text-green-500" />
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-500">Total Roles</p>
                        <p class="text-2xl font-semibold text-gray-900">{{ $stats['total_roles'] }}</p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <x-heroicon-o-cube class="h-8 w-8 text-purple-500" />
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-500">Models with Permissions</p>
                        <p class="text-2xl font-semibold text-gray-900">{{ count($stats['models_with_permissions']) }}</p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <x-heroicon-o-exclamation-triangle class="h-8 w-8 text-yellow-500" />
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-500">Unused Permissions</p>
                        <p class="text-2xl font-semibold text-gray-900">{{ $stats['unused_permissions']->count() }}</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Models with Permissions -->
        <div class="bg-white rounded-lg shadow">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">Models dengan Permissions</h3>
                <p class="text-sm text-gray-500">Daftar models yang sudah memiliki permissions</p>
            </div>
            <div class="p-6">
                <div class="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-2">
                    @foreach($stats['models_with_permissions'] as $model)
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                            {{ $model }}
                        </span>
                    @endforeach
                </div>
            </div>
        </div>

        <!-- Unused Permissions -->
        @if($stats['unused_permissions']->count() > 0)
        <div class="bg-white rounded-lg shadow">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">Permissions yang Tidak Digunakan</h3>
                <p class="text-sm text-gray-500">Permissions yang tidak diassign ke role manapun</p>
            </div>
            <div class="p-6">
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-2">
                    @foreach($stats['unused_permissions'] as $permission)
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                            {{ $permission->name }}
                        </span>
                    @endforeach
                </div>
            </div>
        </div>
        @endif

        <!-- Permissions Table -->
        <div class="bg-white rounded-lg shadow">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">Semua Permissions</h3>
                <p class="text-sm text-gray-500">Kelola semua permissions dalam sistem</p>
            </div>
            <div class="p-6">
                {{ $this->table }}
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="bg-white rounded-lg shadow">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">Quick Actions</h3>
                <p class="text-sm text-gray-500">Aksi cepat untuk mengelola permissions</p>
            </div>
            <div class="p-6">
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div class="text-center">
                        <x-heroicon-o-arrow-path class="mx-auto h-12 w-12 text-gray-400" />
                        <h4 class="mt-2 text-sm font-medium text-gray-900">Generate dari Routes</h4>
                        <p class="mt-1 text-sm text-gray-500">Buat permissions berdasarkan routes yang ada</p>
                    </div>
                    
                    <div class="text-center">
                        <x-heroicon-o-cube class="mx-auto h-12 w-12 text-gray-400" />
                        <h4 class="mt-2 text-sm font-medium text-gray-900">Generate dari Models</h4>
                        <p class="mt-1 text-sm text-gray-500">Buat permissions standar untuk semua models</p>
                    </div>
                    
                    <div class="text-center">
                        <x-heroicon-o-shield-check class="mx-auto h-12 w-12 text-gray-400" />
                        <h4 class="mt-2 text-sm font-medium text-gray-900">Sync Super Admin</h4>
                        <p class="mt-1 text-sm text-gray-500">Berikan semua permissions ke super admin</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</x-filament-panels::page>
