<?php

namespace Modules\RajaShield\Filament\Resources\PermissionResource\Pages;

use Filament\Resources\Pages\CreateRecord;
use Modules\RajaShield\Filament\Resources\PermissionResource;
use Filament\Notifications\Notification;

class CreatePermission extends CreateRecord
{
    protected static string $resource = PermissionResource::class;

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }

    protected function getCreatedNotification(): ?Notification
    {
        return Notification::make()
            ->success()
            ->title('Permission berhasil dibuat')
            ->body('Permission baru telah berhasil ditambahkan ke sistem.');
    }

    protected function mutateFormDataBeforeCreate(array $data): array
    {
        // Pastikan guard_name selalu 'web' jika tidak diset
        $data['guard_name'] = $data['guard_name'] ?? 'web';
        
        // Bersihkan nama permission (lowercase, replace spaces dengan underscore)
        $data['name'] = strtolower(str_replace(' ', '_', $data['name']));
        
        return $data;
    }
}
