<?php

namespace Modules\RajaShield\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Route;
use Symfony\Component\HttpFoundation\Response;
use Spatie\Permission\Models\Permission;
use Filament\Notifications\Notification;

class RajaShieldMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Skip jika user belum login
        if (!Auth::check()) {
            return $next($request);
        }

        $user = Auth::user();
        
        // Super admin memiliki akses ke semua
        if ($user->hasRole('super-admin')) {
            return $next($request);
        }

        // Ambil route name saat ini
        $routeName = Route::currentRouteName();
        
        // Skip jika tidak ada route name
        if (!$routeName) {
            return $next($request);
        }

        // Skip untuk route yang dikecualikan
        if ($this->isExcludedRoute($routeName)) {
            return $next($request);
        }

        // Cek permission berdasarkan route name
        if ($this->checkRoutePermission($user, $routeName)) {
            return $next($request);
        }

        // Jika tidak memiliki permission, redirect dengan error
        if ($request->expectsJson()) {
            return response()->json([
                'message' => 'Anda tidak memiliki akses ke halaman ini.',
                'error' => 'Insufficient permissions'
            ], 403);
        }

        // Untuk request web, redirect ke dashboard dengan notifikasi
        Notification::make()
            ->danger()
            ->title('Akses Ditolak')
            ->body('Anda tidak memiliki permission untuk mengakses halaman ini.')
            ->persistent()
            ->send();

        return redirect()->route('filament.admin.pages.dashboard');
    }

    /**
     * Cek apakah user memiliki permission untuk route tertentu
     */
    protected function checkRoutePermission($user, string $routeName): bool
    {
        // Convert route name ke permission name
        $permissionName = $this->routeToPermissionName($routeName);
        
        // Cek apakah permission ada di database
        if (!Permission::where('name', $permissionName)->exists()) {
            // Jika permission tidak ada, izinkan akses (untuk backward compatibility)
            return true;
        }

        // Cek apakah user memiliki permission
        return $user->can($permissionName);
    }

    /**
     * Convert route name ke permission name
     */
    protected function routeToPermissionName(string $routeName): string
    {
        // Convert dot notation ke underscore
        return str_replace('.', '_', $routeName);
    }

    /**
     * Daftar route yang dikecualikan dari pengecekan permission
     */
    protected function isExcludedRoute(string $routeName): bool
    {
        $excludedRoutes = [
            // Auth routes
            'filament.admin.auth.login',
            'filament.admin.auth.logout',
            'filament.admin.auth.password.request',
            'filament.admin.auth.password.reset',
            
            // Dashboard
            'filament.admin.pages.dashboard',
            
            // Profile/Settings
            'filament.admin.auth.profile',
            
            // API routes yang tidak perlu permission
            'sanctum.csrf-cookie',
            
            // Routes yang dimulai dengan prefix tertentu
        ];

        // Cek exact match
        if (in_array($routeName, $excludedRoutes)) {
            return true;
        }

        // Cek prefix match
        $excludedPrefixes = [
            'livewire.',
            'ignition.',
            'telescope.',
            'horizon.',
            '_debugbar.',
        ];

        foreach ($excludedPrefixes as $prefix) {
            if (str_starts_with($routeName, $prefix)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Generate permissions untuk semua routes yang ada
     */
    public static function generatePermissionsFromRoutes(): int
    {
        $routes = Route::getRoutes();
        $generatedCount = 0;

        foreach ($routes as $route) {
            $routeName = $route->getName();
            
            if (!$routeName) {
                continue;
            }

            // Skip route yang dikecualikan
            if ((new self())->isExcludedRoute($routeName)) {
                continue;
            }

            // Generate permission name
            $permissionName = str_replace('.', '_', $routeName);
            
            // Cek apakah permission sudah ada
            if (!Permission::where('name', $permissionName)->exists()) {
                Permission::create([
                    'name' => $permissionName,
                    'guard_name' => 'web',
                    'description' => 'Auto-generated from route: ' . $routeName,
                ]);
                $generatedCount++;
            }
        }

        return $generatedCount;
    }
}
