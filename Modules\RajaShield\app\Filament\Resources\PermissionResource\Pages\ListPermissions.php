<?php

namespace Modules\RajaShield\Filament\Resources\PermissionResource\Pages;

use Filament\Actions;
use Filament\Resources\Pages\ListRecords;
use Filament\Resources\Components\Tab;
use Illuminate\Database\Eloquent\Builder;
use Modules\RajaShield\Filament\Resources\PermissionResource;
use Spatie\Permission\Models\Permission;

class ListPermissions extends ListRecords
{
    protected static string $resource = PermissionResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make()
                ->label('Tambah Permission')
                ->icon('heroicon-o-plus'),
        ];
    }

    public function getTabs(): array
    {
        $tabs = [
            'semua' => Tab::make('Semua Permission')
                ->badge(fn () => Permission::count()),

            'web' => Tab::make('Web Guard')
                ->modifyQueryUsing(fn (Builder $query) => $query->where('guard_name', 'web'))
                ->badge(fn () => Permission::where('guard_name', 'web')->count()),

            'api' => Tab::make('API Guard')
                ->modifyQueryUsing(fn (Builder $query) => $query->where('guard_name', 'api'))
                ->badge(fn () => Permission::where('guard_name', 'api')->count()),
        ];

        // Tambahkan tab berdasarkan kategori
        $categories = $this->getPermissionCategories();
        foreach ($categories as $category => $label) {
            $tabs[$category] = Tab::make($label)
                ->modifyQueryUsing(fn (Builder $query) => $query->where('name', 'like', '%_' . $category . '%'))
                ->badge(fn () => Permission::where('name', 'like', '%_' . $category . '%')->count());
        }

        return $tabs;
    }

    protected function getPermissionCategories(): array
    {
        // Ambil kategori dari permissions yang sudah ada
        $permissions = Permission::all();
        $categories = [];

        foreach ($permissions as $permission) {
            $parts = explode('_', $permission->name);
            if (count($parts) >= 2) {
                $category = $parts[1]; // Ambil bagian kedua sebagai kategori
                $categories[$category] = ucfirst($category);
            }
        }

        return array_slice($categories, 0, 5); // Batasi hanya 5 kategori teratas
    }
}
