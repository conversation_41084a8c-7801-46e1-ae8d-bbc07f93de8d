<?php

namespace Modules\RajaShield\Database\Seeders;

use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;

class RajaShieldSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create basic roles
        $this->createBasicRoles();
        
        // Create basic permissions
        $this->createBasicPermissions();
        
        // Assign permissions to roles
        $this->assignPermissionsToRoles();
    }

    /**
     * Create basic roles
     */
    protected function createBasicRoles(): void
    {
        $roles = [
            [
                'name' => 'super-admin',
                'guard_name' => 'web',
            ],
            [
                'name' => 'admin',
                'guard_name' => 'web',
            ],
            [
                'name' => 'manager',
                'guard_name' => 'web',
            ],
            [
                'name' => 'user',
                'guard_name' => 'web',
            ],
            [
                'name' => 'member',
                'guard_name' => 'web',
            ],
        ];

        foreach ($roles as $roleData) {
            Role::firstOrCreate(
                ['name' => $roleData['name'], 'guard_name' => $roleData['guard_name']],
                $roleData
            );
        }
    }

    /**
     * Create basic permissions
     */
    protected function createBasicPermissions(): void
    {
        $permissions = [
            // Dashboard permissions
            [
                'name' => 'view_dashboard',
                'guard_name' => 'web',
                'description' => 'View dashboard',
                'category' => 'dashboard',
            ],
            
            // User management permissions
            [
                'name' => 'view_users',
                'guard_name' => 'web',
                'description' => 'View users list',
                'category' => 'users',
            ],
            [
                'name' => 'view_any_users',
                'guard_name' => 'web',
                'description' => 'View any user',
                'category' => 'users',
            ],
            [
                'name' => 'create_users',
                'guard_name' => 'web',
                'description' => 'Create new users',
                'category' => 'users',
            ],
            [
                'name' => 'update_users',
                'guard_name' => 'web',
                'description' => 'Update users',
                'category' => 'users',
            ],
            [
                'name' => 'delete_users',
                'guard_name' => 'web',
                'description' => 'Delete users',
                'category' => 'users',
            ],
            
            // Role management permissions
            [
                'name' => 'view_roles',
                'guard_name' => 'web',
                'description' => 'View roles list',
                'category' => 'roles',
            ],
            [
                'name' => 'view_any_roles',
                'guard_name' => 'web',
                'description' => 'View any role',
                'category' => 'roles',
            ],
            [
                'name' => 'create_roles',
                'guard_name' => 'web',
                'description' => 'Create new roles',
                'category' => 'roles',
            ],
            [
                'name' => 'update_roles',
                'guard_name' => 'web',
                'description' => 'Update roles',
                'category' => 'roles',
            ],
            [
                'name' => 'delete_roles',
                'guard_name' => 'web',
                'description' => 'Delete roles',
                'category' => 'roles',
            ],
            
            // Permission management permissions
            [
                'name' => 'view_permissions',
                'guard_name' => 'web',
                'description' => 'View permissions list',
                'category' => 'permissions',
            ],
            [
                'name' => 'view_any_permissions',
                'guard_name' => 'web',
                'description' => 'View any permission',
                'category' => 'permissions',
            ],
            [
                'name' => 'create_permissions',
                'guard_name' => 'web',
                'description' => 'Create new permissions',
                'category' => 'permissions',
            ],
            [
                'name' => 'update_permissions',
                'guard_name' => 'web',
                'description' => 'Update permissions',
                'category' => 'permissions',
            ],
            [
                'name' => 'delete_permissions',
                'guard_name' => 'web',
                'description' => 'Delete permissions',
                'category' => 'permissions',
            ],
        ];

        foreach ($permissions as $permissionData) {
            Permission::firstOrCreate(
                ['name' => $permissionData['name'], 'guard_name' => $permissionData['guard_name']],
                $permissionData
            );
        }
    }

    /**
     * Assign permissions to roles
     */
    protected function assignPermissionsToRoles(): void
    {
        // Super admin gets all permissions
        $superAdmin = Role::where('name', 'super-admin')->first();
        if ($superAdmin) {
            $allPermissions = Permission::where('guard_name', 'web')->get();
            $superAdmin->syncPermissions($allPermissions);
        }

        // Admin gets most permissions except super admin specific ones
        $admin = Role::where('name', 'admin')->first();
        if ($admin) {
            $adminPermissions = Permission::where('guard_name', 'web')
                ->whereIn('category', ['dashboard', 'users'])
                ->get();
            $admin->syncPermissions($adminPermissions);
        }

        // Manager gets limited permissions
        $manager = Role::where('name', 'manager')->first();
        if ($manager) {
            $managerPermissions = Permission::where('guard_name', 'web')
                ->whereIn('name', ['view_dashboard', 'view_users', 'view_any_users'])
                ->get();
            $manager->syncPermissions($managerPermissions);
        }

        // User gets basic permissions
        $user = Role::where('name', 'user')->first();
        if ($user) {
            $userPermissions = Permission::where('guard_name', 'web')
                ->whereIn('name', ['view_dashboard'])
                ->get();
            $user->syncPermissions($userPermissions);
        }

        // Member gets basic permissions
        $member = Role::where('name', 'member')->first();
        if ($member) {
            $memberPermissions = Permission::where('guard_name', 'web')
                ->whereIn('name', ['view_dashboard'])
                ->get();
            $member->syncPermissions($memberPermissions);
        }
    }
}
