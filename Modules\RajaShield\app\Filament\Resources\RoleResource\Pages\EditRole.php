<?php

namespace Modules\RajaShield\Filament\Resources\RoleResource\Pages;

use Filament\Actions;
use Filament\Resources\Pages\EditRecord;
use Modules\RajaShield\Filament\Resources\RoleResource;
use Filament\Notifications\Notification;

class EditRole extends EditRecord
{
    protected static string $resource = RoleResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\DeleteAction::make()
                ->before(function ($record) {
                    // Cegah penghapusan role super-admin
                    if ($record->name === 'super-admin') {
                        throw new \Exception('Role super-admin tidak dapat dihapus!');
                    }
                }),
        ];
    }

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }

    protected function getSavedNotification(): ?Notification
    {
        return Notification::make()
            ->success()
            ->title('Role berhasil diperbarui')
            ->body('Perubahan role telah berhasil disimpan.');
    }

    protected function mutateFormDataBeforeFill(array $data): array
    {
        // Load permissions untuk role ini
        $role = $this->record;
        $data['permissions'] = $role->permissions->pluck('id')->toArray();
        
        return $data;
    }

    protected function mutateFormDataBeforeSave(array $data): array
    {
        // Pastikan guard_name tidak berubah untuk role yang sudah ada
        unset($data['permissions']); // Permissions akan dihandle secara terpisah
        
        return $data;
    }

    protected function afterSave(): void
    {
        // Sync permissions setelah role disimpan
        $permissions = $this->form->getState()['permissions'] ?? [];
        $this->record->syncPermissions($permissions);
    }
}
