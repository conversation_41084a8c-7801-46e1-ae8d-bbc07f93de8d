# Integrasi RajaShield dengan Panel Admin

Dokumen ini menjelaskan cara mengintegrasikan RajaShield dengan panel admin FilamentPHP yang sudah ada.

## Auto-Registration

RajaShield akan otomatis mendaftarkan resources ke panel admin yang bernama 'admin'. Jika panel admin Anda menggunakan nama yang berbeda, Anda perlu melakukan konfigurasi manual.

## Manual Registration

### 1. Daftarkan Resources di AdminPanelProvider

Jika auto-registration tidak bekerja, Anda dapat mendaftarkan resources secara manual di `app/Providers/Filament/AdminPanelProvider.php`:

```php
use Modules\RajaShield\Filament\Resources\RoleResource;
use Modules\RajaShield\Filament\Resources\PermissionResource;
use Modules\RajaShield\Filament\Pages\PermissionManager;

public function panel(Panel $panel): Panel
{
    return $panel
        // ... konfigurasi lainnya
        ->resources([
            // ... resources lainnya
            RoleResource::class,
            PermissionResource::class,
        ])
        ->pages([
            // ... pages lainnya
            PermissionManager::class,
        ]);
}
```

### 2. Tambahkan Middleware

Tambahkan middleware RajaShield ke panel admin:

```php
public function panel(Panel $panel): Panel
{
    return $panel
        // ... konfigurasi lainnya
        ->authMiddleware([
            Authenticate::class,
            \Modules\RajaShield\Http\Middleware\RajaShieldMiddleware::class,
        ]);
}
```

### 3. Konfigurasi Navigation Group

Jika Anda ingin mengubah navigation group, edit di `modules/RajaShield/config/config.php`:

```php
'navigation' => [
    'group' => 'Security', // Ubah nama group
    'group_icon' => 'heroicon-o-shield-check',
    'group_sort' => 1,
],
```

## Kustomisasi Resources

### 1. Override Navigation Group

Jika Anda ingin mengubah navigation group untuk resource tertentu:

```php
// Di RoleResource.php
protected static ?string $navigationGroup = 'User Management';
```

### 2. Override Navigation Sort

```php
// Di RoleResource.php
protected static ?int $navigationSort = 10;
```

### 3. Override Navigation Label

```php
// Di RoleResource.php
protected static ?string $navigationLabel = 'User Roles';
```

## Disable Auto-Registration

Jika Anda ingin mendisable auto-registration dan melakukan registrasi manual:

1. Hapus `RajaShieldAdminProvider` dari `RajaShieldServiceProvider.php`
2. Daftarkan resources secara manual di panel provider Anda

## Multiple Panels

Jika Anda memiliki multiple panels dan ingin RajaShield hanya muncul di panel tertentu:

```php
// Di RajaShieldAdminProvider.php
protected function registerResourcesToAdminPanel(): void
{
    // Hanya register ke panel 'admin'
    if (Filament::hasPanel('admin')) {
        $adminPanel = Filament::getPanel('admin');
        
        $adminPanel->resources([
            RoleResource::class,
            PermissionResource::class,
        ]);
    }
    
    // Jangan register ke panel lain
}
```

## Troubleshooting

### Resources Tidak Muncul

1. Pastikan panel admin bernama 'admin'
2. Cek apakah `RajaShieldAdminProvider` terdaftar di `RajaShieldServiceProvider`
3. Clear cache: `php artisan cache:clear`

### Permission Denied

1. Pastikan user memiliki role 'super-admin' atau permission yang sesuai
2. Jalankan: `php artisan rajashield:setup --super-admin=<EMAIL>`

### Middleware Tidak Bekerja

1. Pastikan middleware terdaftar di `Kernel.php` atau panel provider
2. Cek konfigurasi excluded routes di `config/config.php`

## Best Practices

1. **Selalu backup database** sebelum menjalankan setup command
2. **Gunakan super-admin role** untuk user pertama
3. **Test permissions** di environment development dulu
4. **Monitor performance** jika menggunakan banyak permissions
5. **Regular cleanup** permissions yang tidak digunakan
