<?php

namespace Modules\RajaShield\Providers;

use Illuminate\Support\Facades\Blade;
use Illuminate\Support\ServiceProvider;
use Nwidart\Modules\Traits\PathNamespace;
use RecursiveDirectoryIterator;
use RecursiveIteratorIterator;
use Filament\Support\Facades\FilamentIcon;
use Illuminate\Support\Facades\Gate;
use Modules\RajaShield\Helpers\SuperAdminHelper;

class RajaShieldServiceProvider extends ServiceProvider
{
    use PathNamespace;

    protected string $name = 'RajaShield';

    protected string $nameLower = 'rajashield';

    /**
     * Boot the application events.
     */
    public function boot(): void
    {
        $this->registerCommands();
        $this->registerCommandSchedules();
        $this->registerTranslations();
        $this->registerConfig();
        $this->registerViews();
        $this->loadMigrationsFrom(module_path($this->name, 'database/migrations'));
        $this->registerMiddleware();
        $this->registerPolicies();
        $this->registerIcons();
        $this->publishAssets();
    }

    /**
     * Register the service provider.
     */
    public function register(): void
    {
        $this->app->register(EventServiceProvider::class);
        $this->app->register(RouteServiceProvider::class);
        $this->app->register(RajaShieldPanelProvider::class);
        $this->registerHelpers();
    }

    /**
     * Register commands in the format of Command::class
     */
    protected function registerCommands(): void
    {
        $this->commands([
            \Modules\RajaShield\Console\Commands\RajaShieldSetupCommand::class,
        ]);
    }

    /**
     * Register command Schedules.
     */
    protected function registerCommandSchedules(): void
    {
        // $this->app->booted(function () {
        //     $schedule = $this->app->make(Schedule::class);
        //     $schedule->command('inspire')->hourly();
        // });
    }

    /**
     * Register translations.
     */
    public function registerTranslations(): void
    {
        $langPath = resource_path('lang/modules/'.$this->nameLower);

        if (is_dir($langPath)) {
            $this->loadTranslationsFrom($langPath, $this->nameLower);
            $this->loadJsonTranslationsFrom($langPath);
        } else {
            $this->loadTranslationsFrom(module_path($this->name, 'lang'), $this->nameLower);
            $this->loadJsonTranslationsFrom(module_path($this->name, 'lang'));
        }
    }

    /**
     * Register config.
     */
    protected function registerConfig(): void
    {
        $relativeConfigPath = config('modules.paths.generator.config.path');
        $configPath = module_path($this->name, $relativeConfigPath);

        if (is_dir($configPath)) {
            $iterator = new RecursiveIteratorIterator(new RecursiveDirectoryIterator($configPath));

            foreach ($iterator as $file) {
                if ($file->isFile() && $file->getExtension() === 'php') {
                    $relativePath = str_replace($configPath . DIRECTORY_SEPARATOR, '', $file->getPathname());
                    $configKey = $this->nameLower . '.' . str_replace([DIRECTORY_SEPARATOR, '.php'], ['.', ''], $relativePath);
                    $key = ($relativePath === 'config.php') ? $this->nameLower : $configKey;

                    $this->publishes([$file->getPathname() => config_path($relativePath)], 'config');
                    $this->mergeConfigFrom($file->getPathname(), $key);
                }
            }
        }
    }

    /**
     * Register views.
     */
    public function registerViews(): void
    {
        $viewPath = resource_path('views/modules/'.$this->nameLower);
        $sourcePath = module_path($this->name, 'resources/views');

        $this->publishes([$sourcePath => $viewPath], ['views', $this->nameLower.'-module-views']);

        $this->loadViewsFrom(array_merge($this->getPublishableViewPaths(), [$sourcePath]), $this->nameLower);

        $componentNamespace = $this->module_namespace($this->name, $this->app_path(config('modules.paths.generator.component-class.path')));
        Blade::componentNamespace($componentNamespace, $this->nameLower);
    }

    /**
     * Get the services provided by the provider.
     */
    public function provides(): array
    {
        return [];
    }

    private function getPublishableViewPaths(): array
    {
        $paths = [];
        foreach (config('view.paths') as $path) {
            if (is_dir($path.'/modules/'.$this->nameLower)) {
                $paths[] = $path.'/modules/'.$this->nameLower;
            }
        }

        return $paths;
    }

    /**
     * Register middleware.
     */
    protected function registerMiddleware(): void
    {
        $router = $this->app['router'];

        // Register middleware alias
        $router->aliasMiddleware('raja.shield', \Modules\RajaShield\Http\Middleware\RajaShieldMiddleware::class);
    }

    /**
     * Register policies.
     */
    protected function registerPolicies(): void
    {
        // Register any custom policies here if needed
        // Gate::policy(Model::class, ModelPolicy::class);
    }

    /**
     * Register icons.
     */
    protected function registerIcons(): void
    {
        // Register custom icons for FilamentPHP if needed
        // FilamentIcon::register([
        //     'raja-shield' => 'heroicon-o-shield-check',
        // ]);
    }

    /**
     * Publish assets.
     */
    protected function publishAssets(): void
    {
        // Publish CSS/JS assets if needed
        $this->publishes([
            module_path($this->name, 'resources/assets') => public_path('modules/' . $this->nameLower),
        ], 'raja-shield-assets');
    }

    /**
     * Register helpers.
     */
    protected function registerHelpers(): void
    {
        // Register helper classes as singletons
        $this->app->singleton('raja-shield.super-admin', function () {
            return new \Modules\RajaShield\Helpers\SuperAdminHelper();
        });

        $this->app->singleton('raja-shield.model-permission', function () {
            return new \Modules\RajaShield\Helpers\ModelPermissionHelper();
        });
    }
}
