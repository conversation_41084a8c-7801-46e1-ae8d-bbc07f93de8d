<?php

namespace Modules\RajaShield\Filament\Resources\PermissionResource\Pages;

use Filament\Actions;
use Filament\Resources\Pages\EditRecord;
use Modules\RajaShield\Filament\Resources\PermissionResource;
use Filament\Notifications\Notification;

class EditPermission extends EditRecord
{
    protected static string $resource = PermissionResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\DeleteAction::make(),
        ];
    }

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }

    protected function getSavedNotification(): ?Notification
    {
        return Notification::make()
            ->success()
            ->title('Permission berhasil diperbarui')
            ->body('Perubahan permission telah berhasil disimpan.');
    }

    protected function mutateFormDataBeforeSave(array $data): array
    {
        // Bersihkan nama permission (lowercase, replace spaces dengan underscore)
        $data['name'] = strtolower(str_replace(' ', '_', $data['name']));
        
        return $data;
    }
}
