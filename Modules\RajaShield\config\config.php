<?php

return [
    'name' => 'RajaShield',

    /*
    |--------------------------------------------------------------------------
    | Super Admin Role Name
    |--------------------------------------------------------------------------
    |
    | Nama role untuk super admin. User dengan role ini akan memiliki
    | akses ke semua permissions tanpa perlu dicek satu per satu.
    |
    */
    'super_admin_role' => 'super-admin',

    /*
    |--------------------------------------------------------------------------
    | Default Guard Name
    |--------------------------------------------------------------------------
    |
    | Guard name default yang akan digunakan untuk permissions dan roles.
    |
    */
    'default_guard' => 'web',

    /*
    |--------------------------------------------------------------------------
    | Cache Settings
    |--------------------------------------------------------------------------
    |
    | Pengaturan cache untuk permission checking.
    |
    */
    'cache' => [
        'enabled' => true,
        'duration' => 300, // 5 menit dalam detik
        'key_prefix' => 'raja_shield_',
    ],

    /*
    |--------------------------------------------------------------------------
    | Middleware Settings
    |--------------------------------------------------------------------------
    |
    | Pengaturan untuk middleware RajaShield.
    |
    */
    'middleware' => [
        'enabled' => true,
        'excluded_routes' => [
            'filament.admin.auth.login',
            'filament.admin.auth.logout',
            'filament.admin.auth.password.request',
            'filament.admin.auth.password.reset',
            'filament.admin.pages.dashboard',
            'filament.admin.auth.profile',
            'sanctum.csrf-cookie',
        ],
        'excluded_prefixes' => [
            'livewire.',
            'ignition.',
            'telescope.',
            'horizon.',
            '_debugbar.',
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Auto-Discovery Settings
    |--------------------------------------------------------------------------
    |
    | Pengaturan untuk auto-discovery models dan resources.
    |
    */
    'auto_discovery' => [
        'models' => [
            'enabled' => true,
            'paths' => [
                app_path('Models'),
                base_path('modules/*/app/Models'),
            ],
        ],
        'resources' => [
            'enabled' => true,
            'paths' => [
                app_path('Filament/Resources'),
                base_path('modules/*/app/Filament/Resources'),
            ],
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Standard Permissions
    |--------------------------------------------------------------------------
    |
    | Permissions standar yang akan dibuat untuk setiap model.
    |
    */
    'standard_permissions' => [
        'view',
        'view_any',
        'create',
        'update',
        'delete',
        'delete_any',
        'force_delete',
        'force_delete_any',
        'restore',
        'restore_any',
        'replicate',
        'reorder',
    ],

    /*
    |--------------------------------------------------------------------------
    | Navigation Settings
    |--------------------------------------------------------------------------
    |
    | Pengaturan untuk navigation group di panel admin.
    |
    */
    'navigation' => [
        'group' => 'Manajemen Akses',
        'group_icon' => 'heroicon-o-shield-check',
        'group_sort' => 1,
    ],
];
