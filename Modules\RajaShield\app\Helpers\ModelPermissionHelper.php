<?php

namespace Modules\RajaShield\Helpers;

use Illuminate\Support\Facades\File;
use Illuminate\Support\Str;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;

class ModelPermissionHelper
{
    /**
     * Standard permissions untuk setiap model
     */
    protected static array $standardPermissions = [
        'view',
        'view_any', 
        'create',
        'update',
        'delete',
        'delete_any',
        'force_delete',
        'force_delete_any',
        'restore',
        'restore_any',
        'replicate',
        'reorder',
    ];

    /**
     * Generate permissions untuk model tertentu
     */
    public static function generateModelPermissions(string $modelName): array
    {
        $modelSlug = Str::snake(Str::plural($modelName));
        $permissions = [];

        foreach (static::$standardPermissions as $action) {
            $permissionName = "{$action}_{$modelSlug}";
            
            $permission = Permission::firstOrCreate([
                'name' => $permissionName,
                'guard_name' => 'web',
            ], [
                'description' => "Permission to {$action} {$modelName}",
            ]);

            $permissions[] = $permission;
        }

        return $permissions;
    }

    /**
     * Auto-discover semua models dan generate permissions
     */
    public static function generateAllModelPermissions(): array
    {
        $models = static::discoverModels();
        $allPermissions = [];

        foreach ($models as $model) {
            $permissions = static::generateModelPermissions($model);
            $allPermissions = array_merge($allPermissions, $permissions);
        }

        // Sync ke super admin
        SuperAdminHelper::syncAllPermissionsToSuperAdmin();

        return $allPermissions;
    }

    /**
     * Discover semua models di aplikasi
     */
    public static function discoverModels(): array
    {
        $models = [];

        // Scan app/Models directory
        $modelsPath = app_path('Models');
        if (File::exists($modelsPath)) {
            $models = array_merge($models, static::scanModelsInDirectory($modelsPath, 'App\\Models\\'));
        }

        // Scan modules
        $modulesPath = base_path('modules');
        if (File::exists($modulesPath)) {
            $moduleDirectories = File::directories($modulesPath);
            
            foreach ($moduleDirectories as $moduleDir) {
                $moduleModelsPath = $moduleDir . '/app/Models';
                if (File::exists($moduleModelsPath)) {
                    $moduleName = basename($moduleDir);
                    $namespace = "Modules\\{$moduleName}\\Models\\";
                    $models = array_merge($models, static::scanModelsInDirectory($moduleModelsPath, $namespace));
                }
            }
        }

        return array_unique($models);
    }

    /**
     * Scan models dalam direktori tertentu
     */
    protected static function scanModelsInDirectory(string $directory, string $namespace): array
    {
        $models = [];
        $files = File::allFiles($directory);

        foreach ($files as $file) {
            if ($file->getExtension() === 'php') {
                $className = $file->getFilenameWithoutExtension();
                $fullClassName = $namespace . $className;

                // Cek apakah class adalah Eloquent model
                if (class_exists($fullClassName) && is_subclass_of($fullClassName, \Illuminate\Database\Eloquent\Model::class)) {
                    $models[] = $className;
                }
            }
        }

        return $models;
    }

    /**
     * Generate permissions untuk FilamentPHP resources
     */
    public static function generateResourcePermissions(): array
    {
        $resources = static::discoverFilamentResources();
        $allPermissions = [];

        foreach ($resources as $resource) {
            $resourceName = static::getResourceModelName($resource);
            if ($resourceName) {
                $permissions = static::generateModelPermissions($resourceName);
                $allPermissions = array_merge($allPermissions, $permissions);
            }
        }

        return $allPermissions;
    }

    /**
     * Discover semua FilamentPHP resources
     */
    public static function discoverFilamentResources(): array
    {
        $resources = [];

        // Scan app/Filament/Resources
        $resourcesPath = app_path('Filament/Resources');
        if (File::exists($resourcesPath)) {
            $resources = array_merge($resources, static::scanResourcesInDirectory($resourcesPath, 'App\\Filament\\Resources\\'));
        }

        // Scan modules
        $modulesPath = base_path('modules');
        if (File::exists($modulesPath)) {
            $moduleDirectories = File::directories($modulesPath);
            
            foreach ($moduleDirectories as $moduleDir) {
                $moduleResourcesPath = $moduleDir . '/app/Filament/Resources';
                if (File::exists($moduleResourcesPath)) {
                    $moduleName = basename($moduleDir);
                    $namespace = "Modules\\{$moduleName}\\Filament\\Resources\\";
                    $resources = array_merge($resources, static::scanResourcesInDirectory($moduleResourcesPath, $namespace));
                }
            }
        }

        return array_unique($resources);
    }

    /**
     * Scan resources dalam direktori tertentu
     */
    protected static function scanResourcesInDirectory(string $directory, string $namespace): array
    {
        $resources = [];
        $files = File::allFiles($directory);

        foreach ($files as $file) {
            if ($file->getExtension() === 'php' && Str::endsWith($file->getFilename(), 'Resource.php')) {
                $className = $file->getFilenameWithoutExtension();
                $fullClassName = $namespace . $className;

                // Cek apakah class adalah Filament Resource
                if (class_exists($fullClassName) && is_subclass_of($fullClassName, \Filament\Resources\Resource::class)) {
                    $resources[] = $fullClassName;
                }
            }
        }

        return $resources;
    }

    /**
     * Get model name dari resource class
     */
    protected static function getResourceModelName(string $resourceClass): ?string
    {
        if (!class_exists($resourceClass)) {
            return null;
        }

        try {
            $reflection = new \ReflectionClass($resourceClass);
            $modelProperty = $reflection->getProperty('model');
            $modelProperty->setAccessible(true);
            $modelClass = $modelProperty->getValue();

            if ($modelClass && class_exists($modelClass)) {
                return class_basename($modelClass);
            }
        } catch (\Exception $e) {
            // Ignore errors
        }

        return null;
    }

    /**
     * Assign permissions ke role berdasarkan pattern
     */
    public static function assignPermissionsToRole(string $roleName, array $patterns): void
    {
        $role = Role::where('name', $roleName)->first();
        if (!$role) {
            return;
        }

        $permissions = [];
        foreach ($patterns as $pattern) {
            $matchingPermissions = Permission::where('name', 'like', $pattern)->get();
            $permissions = array_merge($permissions, $matchingPermissions->toArray());
        }

        $role->syncPermissions(collect($permissions)->pluck('name')->toArray());
    }

    /**
     * Get permissions berdasarkan model
     */
    public static function getModelPermissions(string $modelName): \Illuminate\Database\Eloquent\Collection
    {
        $modelSlug = Str::snake(Str::plural($modelName));
        return Permission::where('name', 'like', "%_{$modelSlug}")->get();
    }

    /**
     * Cek apakah model memiliki permissions
     */
    public static function modelHasPermissions(string $modelName): bool
    {
        return static::getModelPermissions($modelName)->isNotEmpty();
    }

    /**
     * Delete permissions untuk model tertentu
     */
    public static function deleteModelPermissions(string $modelName): int
    {
        $permissions = static::getModelPermissions($modelName);
        $count = $permissions->count();
        
        foreach ($permissions as $permission) {
            $permission->delete();
        }

        return $count;
    }

    /**
     * Get statistik permissions
     */
    public static function getPermissionStats(): array
    {
        return [
            'total_permissions' => Permission::count(),
            'total_roles' => Role::count(),
            'models_with_permissions' => static::getModelsWithPermissions(),
            'unused_permissions' => static::getUnusedPermissions(),
        ];
    }

    /**
     * Get models yang memiliki permissions
     */
    protected static function getModelsWithPermissions(): array
    {
        $models = static::discoverModels();
        $modelsWithPermissions = [];

        foreach ($models as $model) {
            if (static::modelHasPermissions($model)) {
                $modelsWithPermissions[] = $model;
            }
        }

        return $modelsWithPermissions;
    }

    /**
     * Get permissions yang tidak digunakan oleh role manapun
     */
    protected static function getUnusedPermissions(): \Illuminate\Database\Eloquent\Collection
    {
        return Permission::doesntHave('roles')->get();
    }
}
