<?php

namespace Modules\RajaShield\Filament\Resources\RoleResource\Pages;

use Filament\Resources\Pages\CreateRecord;
use Modules\RajaShield\Filament\Resources\RoleResource;
use Filament\Notifications\Notification;

class CreateRole extends CreateRecord
{
    protected static string $resource = RoleResource::class;

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }

    protected function getCreatedNotification(): ?Notification
    {
        return Notification::make()
            ->success()
            ->title('Role berhasil dibuat')
            ->body('Role baru telah berhasil ditambahkan ke sistem.');
    }

    protected function mutateFormDataBeforeCreate(array $data): array
    {
        // Pastikan guard_name selalu 'web' jika tidak diset
        $data['guard_name'] = $data['guard_name'] ?? 'web';
        
        return $data;
    }
}
